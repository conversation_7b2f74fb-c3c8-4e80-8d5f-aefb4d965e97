"""
PDF Chunking Pipeline for Open WebUI
Handles large PDF files by chunking them before processing
"""

import os
import base64
import tempfile
import asyncio
import aiohttp
from typing import List, Dict, Optional, Union
from pydantic import BaseModel, Field
import PyPDF2
import io

class Pipeline:
    class Valves(BaseModel):
        pipelines: List[str] = ["*"]
        priority: int = 2  # Run after document converter
        
        # PDF processing settings
        enable_pdf_chunking: bool = Field(
            default=True,
            description="Enable PDF chunking for large files"
        )
        
        max_pdf_size_mb: float = Field(
            default=1.0,
            description="Max PDF size in MB before chunking"
        )
        
        pages_per_chunk: int = Field(
            default=5,
            description="Number of pages per chunk"
        )
        
        # Docling settings
        docling_url: str = Field(
            default="http://docling-server:5001",
            description="Docling service URL"
        )
        
        # Processing settings
        max_concurrent_chunks: int = Field(
            default=3,
            description="Max concurrent chunk processing"
        )
        
        chunk_timeout: int = Field(
            default=60,
            description="Timeout per chunk (seconds)"
        )
        
        # Debug
        enable_debug: bool = Field(
            default=False,
            description="Enable debug logging"
        )

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()

    async def on_startup(self):
        print(f"📄 PDF Chunking Pipeline started")

    def extract_pdf_from_message(self, content: Union[str, List[Dict]]) -> List[Dict]:
        """Extract PDF attachments from message content"""
        pdfs = []
        
        if isinstance(content, str):
            import re
            pattern = r'!\[(.*?)\]\(data:([^;]+);base64,([^)]+)\)'
            matches = re.findall(pattern, content)
            
            for filename, mime_type, base64_data in matches:
                if mime_type == 'application/pdf':
                    pdfs.append({
                        "filename": filename,
                        "mime_type": mime_type,
                        "data": base64_data
                    })
        
        elif isinstance(content, list):
            for item in content:
                if isinstance(item, dict) and item.get("type") == "image_url":
                    url = item.get("image_url", {}).get("url", "")
                    if url.startswith("data:application/pdf"):
                        try:
                            header, data = url.split(",", 1)
                            pdfs.append({
                                "filename": "document.pdf",
                                "mime_type": "application/pdf",
                                "data": data
                            })
                        except:
                            continue
        
        return pdfs

    def get_pdf_info(self, pdf_data: bytes) -> Dict:
        """Get PDF information (pages, size)"""
        try:
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(pdf_data))
            return {
                "pages": len(pdf_reader.pages),
                "size_mb": len(pdf_data) / (1024 * 1024)
            }
        except Exception as e:
            if self.valves.enable_debug:
                print(f"❌ PDF info error: {e}")
            return {"pages": 0, "size_mb": 0}

    def chunk_pdf(self, pdf_data: bytes, pages_per_chunk: int) -> List[bytes]:
        """Split PDF into chunks"""
        try:
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(pdf_data))
            total_pages = len(pdf_reader.pages)
            chunks = []
            
            for start_page in range(0, total_pages, pages_per_chunk):
                end_page = min(start_page + pages_per_chunk, total_pages)
                
                # Create new PDF with selected pages
                pdf_writer = PyPDF2.PdfWriter()
                for page_num in range(start_page, end_page):
                    pdf_writer.add_page(pdf_reader.pages[page_num])
                
                # Write to bytes
                chunk_buffer = io.BytesIO()
                pdf_writer.write(chunk_buffer)
                chunk_data = chunk_buffer.getvalue()
                chunks.append(chunk_data)
                
                if self.valves.enable_debug:
                    print(f"📄 Created chunk {len(chunks)}: pages {start_page+1}-{end_page}")
            
            return chunks
            
        except Exception as e:
            if self.valves.enable_debug:
                print(f"❌ PDF chunking error: {e}")
            return []

    async def process_pdf_chunk(self, chunk_data: bytes, chunk_index: int, 
                               session: aiohttp.ClientSession) -> Optional[str]:
        """Process a single PDF chunk with Docling"""
        try:
            # Prepare multipart form data
            chunk_base64 = base64.b64encode(chunk_data).decode()
            
            form_data = aiohttp.FormData()
            form_data.add_field('file', 
                              io.BytesIO(chunk_data),
                              filename=f'chunk_{chunk_index}.pdf',
                              content_type='application/pdf')
            
            # Send to Docling
            async with session.post(
                f"{self.valves.docling_url}/v1alpha/convert/file",
                data=form_data,
                timeout=aiohttp.ClientTimeout(total=self.valves.chunk_timeout)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    # Extract text content from Docling response
                    if 'document' in result and 'texts' in result['document']:
                        texts = result['document']['texts']
                        chunk_text = '\n'.join([t.get('text', '') for t in texts])
                        
                        if self.valves.enable_debug:
                            print(f"✅ Processed chunk {chunk_index}: {len(chunk_text)} chars")
                        
                        return chunk_text
                    
                if self.valves.enable_debug:
                    print(f"❌ Chunk {chunk_index} failed: {response.status}")
                return None
                
        except Exception as e:
            if self.valves.enable_debug:
                print(f"❌ Chunk {chunk_index} error: {e}")
            return None

    async def process_large_pdf(self, pdf: Dict) -> Optional[str]:
        """Process large PDF by chunking"""
        try:
            # Decode PDF data
            pdf_data = base64.b64decode(pdf["data"])
            
            # Get PDF info
            pdf_info = self.get_pdf_info(pdf_data)
            
            if pdf_info["size_mb"] <= self.valves.max_pdf_size_mb:
                # Small enough, don't chunk
                return None
            
            if self.valves.enable_debug:
                print(f"📄 Large PDF detected: {pdf_info['size_mb']:.1f}MB, {pdf_info['pages']} pages")
            
            # Chunk the PDF
            chunks = self.chunk_pdf(pdf_data, self.valves.pages_per_chunk)
            
            if not chunks:
                return None
            
            # Process chunks concurrently
            async with aiohttp.ClientSession() as session:
                # Create semaphore to limit concurrent requests
                semaphore = asyncio.Semaphore(self.valves.max_concurrent_chunks)
                
                async def process_chunk_with_semaphore(chunk_data, index):
                    async with semaphore:
                        return await self.process_pdf_chunk(chunk_data, index, session)
                
                # Process all chunks
                tasks = [
                    process_chunk_with_semaphore(chunk_data, i) 
                    for i, chunk_data in enumerate(chunks)
                ]
                
                chunk_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Combine results
                combined_text = []
                successful_chunks = 0
                
                for i, result in enumerate(chunk_results):
                    if isinstance(result, str) and result:
                        combined_text.append(f"--- Chunk {i+1} ---\n{result}")
                        successful_chunks += 1
                    elif self.valves.enable_debug:
                        print(f"❌ Chunk {i+1} failed: {result}")
                
                if successful_chunks > 0:
                    final_text = '\n\n'.join(combined_text)
                    
                    if self.valves.enable_debug:
                        print(f"✅ PDF chunking complete: {successful_chunks}/{len(chunks)} chunks, {len(final_text)} chars")
                    
                    return final_text
                
                return None
                
        except Exception as e:
            if self.valves.enable_debug:
                print(f"❌ Large PDF processing error: {e}")
            return None

    def replace_pdf_with_text(self, content: Union[str, List[Dict]], 
                             pdf: Dict, extracted_text: str) -> Union[str, List[Dict]]:
        """Replace PDF with extracted text in content"""
        
        replacement_text = f"\n\n📄 **Extracted from {pdf['filename']}:**\n\n{extracted_text}\n\n"
        
        if isinstance(content, str):
            # Replace PDF markdown with text
            pdf_pattern = f"![{pdf['filename']}](data:{pdf['mime_type']};base64,{pdf['data']})"
            return content.replace(pdf_pattern, replacement_text)
        
        elif isinstance(content, list):
            # Replace in list format
            for i, item in enumerate(content):
                if isinstance(item, dict) and item.get("type") == "image_url":
                    url = item.get("image_url", {}).get("url", "")
                    if f"data:{pdf['mime_type']};base64,{pdf['data']}" in url:
                        # Replace with text item
                        content[i] = {
                            "type": "text",
                            "text": replacement_text
                        }
        
        return content

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Process messages and handle large PDFs"""
        
        if not self.valves.enable_pdf_chunking:
            return body
        
        messages = body.get("messages", [])
        if not messages:
            return body
        
        modified = False
        
        # Process each message
        for message in messages:
            if message.get("role") != "user":
                continue
                
            content = message.get("content")
            if not content:
                continue
            
            # Extract PDFs
            pdfs = self.extract_pdf_from_message(content)
            
            if not pdfs:
                continue
            
            if self.valves.enable_debug:
                print(f"📄 Found {len(pdfs)} PDF(s) for processing")
            
            # Process each PDF
            for pdf in pdfs:
                extracted_text = await self.process_large_pdf(pdf)
                
                if extracted_text:
                    # Replace PDF with extracted text
                    content = self.replace_pdf_with_text(content, pdf, extracted_text)
                    message["content"] = content
                    modified = True
                    
                    if self.valves.enable_debug:
                        print(f"✅ Replaced {pdf['filename']} with extracted text")
        
        if modified and self.valves.enable_debug:
            print("📄 PDF chunking pipeline completed")
        
        return body
