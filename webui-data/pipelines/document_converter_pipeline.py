"""
Document Converter Pipeline for Open WebUI
Auto-converts .doc to .docx, .xls to .xlsx before processing
"""

import os
import base64
import tempfile
import subprocess
import asyncio
from typing import List, Dict, Optional, Union
from pydantic import BaseModel, Field

class Pipeline:
    class Valves(BaseModel):
        pipelines: List[str] = ["*"]
        priority: int = 1  # High priority to run before other pipelines
        
        # Conversion settings
        enable_auto_conversion: bool = Field(
            default=True,
            description="Enable automatic document conversion"
        )
        
        # Supported conversions
        convert_doc_to_docx: bool = Field(
            default=True,
            description="Convert .doc files to .docx"
        )
        
        convert_xls_to_xlsx: bool = Field(
            default=True,
            description="Convert .xls files to .xlsx"
        )
        
        # LibreOffice settings
        libreoffice_timeout: int = Field(
            default=30,
            description="Timeout for LibreOffice conversion (seconds)"
        )
        
        # Debug
        enable_debug: bool = Field(
            default=False,
            description="Enable debug logging"
        )

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        
        # Conversion mappings
        self.conversion_map = {
            'application/msword': 'docx',  # .doc -> .docx
            'application/vnd.ms-excel': 'xlsx',  # .xls -> .xlsx
        }

    async def on_startup(self):
        print(f"🔄 Document Converter Pipeline started")
        
        # Check if LibreOffice is available
        try:
            result = subprocess.run(['libreoffice', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ LibreOffice found for document conversion")
            else:
                print("❌ LibreOffice not found - auto conversion disabled")
                self.valves.enable_auto_conversion = False
        except Exception as e:
            print(f"❌ LibreOffice check failed: {e}")
            self.valves.enable_auto_conversion = False

    def extract_documents_from_message(self, content: Union[str, List[Dict]]) -> List[Dict]:
        """Extract document attachments from message content"""
        documents = []
        
        if isinstance(content, str):
            # Look for base64 data in markdown format
            import re
            pattern = r'!\[(.*?)\]\(data:([^;]+);base64,([^)]+)\)'
            matches = re.findall(pattern, content)
            
            for filename, mime_type, base64_data in matches:
                if mime_type in self.conversion_map:
                    documents.append({
                        "filename": filename,
                        "mime_type": mime_type,
                        "data": base64_data
                    })
        
        elif isinstance(content, list):
            for item in content:
                if isinstance(item, dict) and item.get("type") == "image_url":
                    url = item.get("image_url", {}).get("url", "")
                    if url.startswith("data:"):
                        # Parse data URL
                        try:
                            header, data = url.split(",", 1)
                            mime_type = header.split(":")[1].split(";")[0]
                            if mime_type in self.conversion_map:
                                documents.append({
                                    "filename": "document",
                                    "mime_type": mime_type,
                                    "data": data
                                })
                        except:
                            continue
        
        return documents

    async def convert_document(self, doc: Dict) -> Optional[Dict]:
        """Convert document using LibreOffice"""
        if not self.valves.enable_auto_conversion:
            return None
            
        try:
            # Decode base64 data
            file_data = base64.b64decode(doc["data"])
            
            # Determine target format
            target_format = self.conversion_map.get(doc["mime_type"])
            if not target_format:
                return None
            
            # Create temporary files
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{doc['filename'].split('.')[-1]}") as input_file:
                input_file.write(file_data)
                input_path = input_file.name
            
            output_dir = tempfile.mkdtemp()
            
            try:
                # Run LibreOffice conversion
                cmd = [
                    'libreoffice',
                    '--headless',
                    '--convert-to', target_format,
                    '--outdir', output_dir,
                    input_path
                ]
                
                if self.valves.enable_debug:
                    print(f"🔄 Converting {doc['filename']} to {target_format}")
                
                result = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    result.communicate(), 
                    timeout=self.valves.libreoffice_timeout
                )
                
                if result.returncode != 0:
                    if self.valves.enable_debug:
                        print(f"❌ Conversion failed: {stderr.decode()}")
                    return None
                
                # Find converted file
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_path = os.path.join(output_dir, f"{base_name}.{target_format}")
                
                if not os.path.exists(output_path):
                    return None
                
                # Read converted file
                with open(output_path, 'rb') as f:
                    converted_data = f.read()
                
                # Encode to base64
                converted_base64 = base64.b64encode(converted_data).decode()
                
                # Update filename and mime type
                new_filename = f"{os.path.splitext(doc['filename'])[0]}.{target_format}"
                new_mime_type = {
                    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }.get(target_format)
                
                if self.valves.enable_debug:
                    print(f"✅ Converted {doc['filename']} -> {new_filename}")
                
                return {
                    "filename": new_filename,
                    "mime_type": new_mime_type,
                    "data": converted_base64,
                    "original_filename": doc["filename"]
                }
                
            finally:
                # Cleanup
                try:
                    os.unlink(input_path)
                    import shutil
                    shutil.rmtree(output_dir)
                except:
                    pass
                    
        except Exception as e:
            if self.valves.enable_debug:
                print(f"❌ Conversion error: {e}")
            return None

    def replace_document_in_content(self, content: Union[str, List[Dict]], 
                                   original_doc: Dict, converted_doc: Dict) -> Union[str, List[Dict]]:
        """Replace original document with converted version in content"""
        
        if isinstance(content, str):
            # Replace in markdown format
            old_pattern = f"![{original_doc['filename']}](data:{original_doc['mime_type']};base64,{original_doc['data']})"
            new_pattern = f"![{converted_doc['filename']}](data:{converted_doc['mime_type']};base64,{converted_doc['data']})"
            return content.replace(old_pattern, new_pattern)
        
        elif isinstance(content, list):
            # Replace in list format
            for item in content:
                if isinstance(item, dict) and item.get("type") == "image_url":
                    url = item.get("image_url", {}).get("url", "")
                    if f"data:{original_doc['mime_type']};base64,{original_doc['data']}" in url:
                        item["image_url"]["url"] = f"data:{converted_doc['mime_type']};base64,{converted_doc['data']}"
        
        return content

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Process messages and convert documents"""
        
        if not self.valves.enable_auto_conversion:
            return body
        
        messages = body.get("messages", [])
        if not messages:
            return body
        
        modified = False
        
        # Process each message
        for message in messages:
            if message.get("role") != "user":
                continue
                
            content = message.get("content")
            if not content:
                continue
            
            # Extract documents that need conversion
            documents = self.extract_documents_from_message(content)
            
            if not documents:
                continue
            
            if self.valves.enable_debug:
                print(f"📄 Found {len(documents)} document(s) for potential conversion")
            
            # Convert each document
            for doc in documents:
                converted_doc = await self.convert_document(doc)
                
                if converted_doc:
                    # Replace in message content
                    content = self.replace_document_in_content(content, doc, converted_doc)
                    message["content"] = content
                    modified = True
                    
                    if self.valves.enable_debug:
                        print(f"✅ Replaced {doc['filename']} with {converted_doc['filename']}")
        
        if modified and self.valves.enable_debug:
            print("🔄 Document conversion pipeline completed")
        
        return body
