"""
Docling Extension for .doc/.xls support
Adds automatic conversion capabilities to docling-serve
"""

import os
import tempfile
import subprocess
import magic
from pathlib import Path
from typing import Optional, Dict, Any

class DocumentConverter:
    """Handles conversion of legacy document formats"""
    
    def __init__(self):
        self.libreoffice_path = os.getenv('LIBREOFFICE_PATH', '/usr/bin/libreoffice')
        self.conversion_map = {
            'application/msword': 'docx',  # .doc -> .docx
            'application/vnd.ms-excel': 'xlsx',  # .xls -> .xlsx
        }
    
    def detect_mime_type(self, file_path: str) -> str:
        """Detect MIME type of file"""
        try:
            mime = magic.Magic(mime=True)
            return mime.from_file(file_path)
        except Exception:
            # Fallback to file extension
            ext = Path(file_path).suffix.lower()
            if ext == '.doc':
                return 'application/msword'
            elif ext == '.xls':
                return 'application/vnd.ms-excel'
            return 'application/octet-stream'
    
    def needs_conversion(self, file_path: str) -> bool:
        """Check if file needs conversion"""
        mime_type = self.detect_mime_type(file_path)
        return mime_type in self.conversion_map
    
    def convert_document(self, input_path: str, output_dir: str) -> Optional[str]:
        """Convert document using LibreOffice"""
        try:
            mime_type = self.detect_mime_type(input_path)
            target_format = self.conversion_map.get(mime_type)
            
            if not target_format:
                return None
            
            # Run LibreOffice conversion
            cmd = [
                self.libreoffice_path,
                '--headless',
                '--convert-to', target_format,
                '--outdir', output_dir,
                input_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode != 0:
                print(f"Conversion failed: {result.stderr}")
                return None
            
            # Find converted file
            input_name = Path(input_path).stem
            converted_file = Path(output_dir) / f"{input_name}.{target_format}"
            
            if converted_file.exists():
                return str(converted_file)
            
            return None
            
        except Exception as e:
            print(f"Conversion error: {e}")
            return None

# Monkey patch for docling-serve
def patch_docling_serve():
    """Patch docling-serve to support .doc/.xls"""
    try:
        # Import docling modules
        from docling.datamodel.base_models import InputFormat
        from docling_serve.docling_conversion import DoclingConversion
        
        # Add new input formats
        if not hasattr(InputFormat, 'DOC'):
            InputFormat.DOC = 'doc'
        if not hasattr(InputFormat, 'XLS'):
            InputFormat.XLS = 'xls'
        
        # Store original convert method
        original_convert = DoclingConversion.convert
        
        def enhanced_convert(self, source, **kwargs):
            """Enhanced convert with .doc/.xls support"""
            converter = DocumentConverter()
            
            # Check if source needs conversion
            if isinstance(source, str) and os.path.isfile(source):
                if converter.needs_conversion(source):
                    # Create temp directory for conversion
                    with tempfile.TemporaryDirectory() as temp_dir:
                        converted_file = converter.convert_document(source, temp_dir)
                        if converted_file:
                            print(f"Converted {source} -> {converted_file}")
                            # Use converted file as source
                            return original_convert(self, converted_file, **kwargs)
                        else:
                            raise ValueError(f"Failed to convert {source}")
            
            # Use original method for supported formats
            return original_convert(self, source, **kwargs)
        
        # Apply patch
        DoclingConversion.convert = enhanced_convert
        print("✅ Docling patched with .doc/.xls support")
        
    except Exception as e:
        print(f"❌ Failed to patch docling: {e}")

# Auto-patch when module is imported
if os.getenv('ENABLE_DOC_CONVERSION', 'false').lower() == 'true':
    patch_docling_serve()
