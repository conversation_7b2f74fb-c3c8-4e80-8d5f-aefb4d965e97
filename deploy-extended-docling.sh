#!/bin/bash

# Deploy Extended Docling with .doc/.xls support

echo "🔧 Building extended docling container..."

# Stop current docling container
echo "⏹️ Stopping current docling container..."
docker stop docling-server 2>/dev/null || true
docker rm docling-server 2>/dev/null || true

# Build new image
echo "🏗️ Building docling-extended image..."
docker build -f Dockerfile.docling-extended -t docling-extended:latest .

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

# Run new container
echo "🚀 Starting extended docling container..."
docker run -d \
    --name docling-server \
    --network acca-network \
    -p 5002:5001 \
    -e ENABLE_DOC_CONVERSION=true \
    --restart unless-stopped \
    docling-extended:latest

# Wait for container to start
echo "⏳ Waiting for container to start..."
sleep 10

# Test the service
echo "🧪 Testing extended docling service..."
curl -f http://localhost:5002/health > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Extended docling is running successfully!"
    echo "📄 Now supports: PDF, DOCX, XLSX, PPTX, HTML, Images, CSV, MD, DOC, XLS"
    echo "🔗 Service URL: http://localhost:5002"
    echo "📚 API Docs: http://localhost:5002/docs"
else
    echo "❌ Service failed to start properly"
    echo "📋 Checking logs..."
    docker logs docling-server --tail 20
    exit 1
fi

echo "🎉 Deployment complete!"
