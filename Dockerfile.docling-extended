# Extended Docling with .doc/.xls support
FROM quay.io/docling-project/docling-serve:v0.16.1

USER root

# Install LibreOffice for document conversion (CentOS Stream 9)
RUN dnf update -y && \
    dnf install -y \
    libreoffice \
    libreoffice-writer \
    libreoffice-calc \
    python3-pip \
    file \
    && dnf clean all

# Copy custom converter script
COPY docling_converter_extension.py /opt/app-root/src/

# Install additional Python packages
RUN pip install python-magic

USER 1001

# Set environment variables
ENV ENABLE_DOC_CONVERSION=true
ENV LIBREOFFICE_PATH=/usr/bin/libreoffice

EXPOSE 5001
